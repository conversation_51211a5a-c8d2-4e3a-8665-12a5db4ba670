import React from 'react';

interface InactivityWarningProps {
  onContinue: () => void;
  warningMinutes?: number;
}

const InactivityWarning: React.FC<InactivityWarningProps> = ({ 
  onContinue,
  warningMinutes = 1
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 text-red-600">Session Timeout Warning</h2>
        <p className="mb-4">
          Your session will expire in {warningMinutes} {warningMinutes === 1 ? 'minute' : 'minutes'} due to inactivity.
        </p>
        <p className="mb-6">
          Click "Continue Session" to stay logged in.
        </p>
        <div className="flex justify-end">
          <button
            onClick={onContinue}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Continue Session
          </button>
        </div>
      </div>
    </div>
  );
};

export default InactivityWarning;
