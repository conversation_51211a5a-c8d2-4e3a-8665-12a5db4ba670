import React, { useState } from 'react';
import { auth, db } from '../firebase/config';
import { collection, getDocs, addDoc, deleteDoc, doc } from 'firebase/firestore';

const FirebaseConnectionTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      // Test 1: Check authentication
      addTestResult('🔍 Testing authentication...');
      const user = auth.currentUser;
      if (!user) {
        addTestResult('❌ No user authenticated');
        setIsRunning(false);
        return;
      }
      addTestResult(`✅ User authenticated: ${user.email}`);

      // Test 2: Test Firestore read
      addTestResult('🔍 Testing Firestore read...');
      const productsRef = collection(db, 'products');
      const snapshot = await getDocs(productsRef);
      addTestResult(`✅ Firestore read successful: ${snapshot.size} documents found`);

      // Test 3: Test Firestore write
      addTestResult('🔍 Testing Firestore write...');
      const testDoc = {
        name: 'Test Product',
        price: '100',
        timestamp: new Date().toISOString(),
        isTest: true
      };
      
      const docRef = await addDoc(productsRef, testDoc);
      addTestResult(`✅ Firestore write successful: Document ID ${docRef.id}`);

      // Test 4: Test Firestore delete (cleanup)
      addTestResult('🔍 Cleaning up test document...');
      await deleteDoc(doc(db, 'products', docRef.id));
      addTestResult('✅ Test document deleted successfully');

      addTestResult('🎉 All tests passed! Firebase connection is working properly.');

    } catch (error: any) {
      addTestResult(`❌ Test failed: ${error.message}`);
      console.error('Firebase test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-3">Firebase Connection Test</h3>
      
      <button
        onClick={runTests}
        disabled={isRunning}
        className={`px-4 py-2 rounded-md text-white ${
          isRunning 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-blue-500 hover:bg-blue-600'
        }`}
      >
        {isRunning ? 'Running Tests...' : 'Run Firebase Tests'}
      </button>

      {testResults.length > 0 && (
        <div className="mt-4">
          <h4 className="font-medium mb-2">Test Results:</h4>
          <div className="bg-white p-3 rounded border max-h-60 overflow-y-auto">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FirebaseConnectionTest;
