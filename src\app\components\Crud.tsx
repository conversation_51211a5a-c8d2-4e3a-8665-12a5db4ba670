import React, { useState, useEffect } from "react";
import { FaTrashAlt } from "react-icons/fa";
import { FiEdit } from "react-icons/fi";
import useFirebaseData from "../hooks/useFirebaseData";
import AddProduct from "../components/Addproduct";
import EditProduct from "../components/EditProduct";
import FirebaseTest from "../components/FirebaseTest";
import FirebaseConnectionTest from "../components/FirebaseConnectionTest";
import { GrCaretNext, GrCaretPrevious } from "react-icons/gr";
import { Item, ProductData } from "../types";

interface CrudProps {
  isActive?: boolean;
}

const Crud: React.FC<CrudProps> = ({ isActive = true }) => {
  const { items, loading, error, deleteItem, updateItem, fetchData, isAuthenticated, authLoading } = useFirebaseData();
  const [editingItem, setEditingItem] = useState<ProductData | null>(null);
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [newlyAddedItem, setNewlyAddedItem] = useState<Item | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);

  // Refresh data when component becomes active
  useEffect(() => {
    console.log('Crud useEffect triggered:', { isActive, isAuthenticated, authLoading });
    if (isActive && isAuthenticated && !authLoading) {
      console.log('Crud component is active, refreshing data...');
      fetchData();
    }
  }, [isActive, isAuthenticated, authLoading, fetchData]);

  // Debug effect to log state changes
  useEffect(() => {
    console.log('Crud state changed:', {
      itemsCount: items.length,
      loading,
      error,
      isAuthenticated,
      authLoading
    });
  }, [items.length, loading, error, isAuthenticated, authLoading]);

  const handleDeleteItem = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      await deleteItem(id);
    }
  };

  const handleEditClick = (item: ProductData) => {
    setEditingItem(item);
  };

  const handleSaveEdit = async () => {
    if (!editingItem) return;

    const updatedData = {
      productName: editingItem.productName,
      price: editingItem.price,
      imageUrl: editingItem.imageUrl,
      productType: editingItem.productType,
      productDetails: editingItem.productDetails,
      filterPrice: editingItem.filterPrice,
      originalPrice: editingItem.originalPrice,
      discountPercent: editingItem.discountPercent,
      storeRating: editingItem.storeRating,
      productReviewRating: editingItem.productReviewRating,
      soldCount: editingItem.soldCount,
      productLink1: editingItem.productLink1,
      productLink2: editingItem.productLink2,
      shopeeLink: editingItem.shopeeLink,
      lazadaLink: editingItem.lazadaLink,
    };

    try {
      if (editingItem.id) { // Ensure editingItem.id is not undefined
        await updateItem(editingItem.id, updatedData);
        console.log("Item updated successfully:", updatedData);
        setEditingItem(null);
      } else {
        console.error("Error: editingItem.id is undefined");
      }
    } catch (error) {
      console.error("Error updating item:", error);
    }
  };



  // Reset newly added item when search query changes
  useEffect(() => {
    if (searchQuery) {
      setNewlyAddedItem(null);
    }
  }, [searchQuery]);

  // Clear the newly added item highlight after 8 seconds
  useEffect(() => {
    if (newlyAddedItem) {
      const timer = setTimeout(() => {
        setNewlyAddedItem(null);
      }, 8000);

      return () => clearTimeout(timer);
    }
  }, [newlyAddedItem]);

  // Filter and sort items based on search query
  const filteredItems = items
    .filter(
      (item) => {
        const typedItem = item as unknown as ProductData;
        return typedItem.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (typedItem.productType &&
            typedItem.productType.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (typedItem.productDetails &&
            typedItem.productDetails
              .toLowerCase()
              .includes(searchQuery.toLowerCase())) ||
          (typedItem.price && typedItem.price.toString().includes(searchQuery)) ||
          (typedItem.filterPrice && typedItem.filterPrice.toString().includes(searchQuery));
      }
    )
    .sort((a, b) => {
      // If we have a newly added item, it should always be at the top
      if (newlyAddedItem && a.id === newlyAddedItem.id) return -1;
      if (newlyAddedItem && b.id === newlyAddedItem.id) return 1;

      // Otherwise, sort by timestamp (latest first)
      const aTyped = a as unknown as ProductData;
      const bTyped = b as unknown as ProductData;
      const dateA = new Date(aTyped.timestamp || 0).getTime();
      const dateB = new Date(bTyped.timestamp || 0).getTime();
      return dateB - dateA;
    });

  // Pagination logic
  const totalPages = Math.ceil(filteredItems.length / rowsPerPage);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to the first page
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Show loading state for authentication
  if (authLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600">กำลังตรวจสอบสิทธิ์การเข้าใช้งาน...</div>
      </div>
    );
  }

  // Show message if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        <div className="text-gray-600 text-lg mb-2">กรุณาเข้าสู่ระบบ</div>
        <div className="text-gray-400 text-sm">คุณต้องเข้าสู่ระบบเพื่อดูและจัดการข้อมูลสินค้า</div>
      </div>
    );
  }

  // Show loading state for data
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600">กำลังโหลดข้อมูลสินค้า...</div>
        <div className="text-gray-400 text-sm mt-2">
          Auth: {isAuthenticated ? 'Authenticated' : 'Not authenticated'} |
          Items: {items.length} |
          Error: {error ? 'Yes' : 'No'}
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Debug information */}
      <div className="mb-2 p-2 bg-gray-100 rounded text-xs text-gray-600">
        Debug: Auth={isAuthenticated ? 'Yes' : 'No'} | AuthLoading={authLoading ? 'Yes' : 'No'} |
        Loading={loading ? 'Yes' : 'No'} | Items={items.length} | Error={error ? 'Yes' : 'No'}
      </div>

      {error && (
        <div className="space-y-4 mb-4">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded flex items-center justify-between">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>{error}</span>
            </div>
            <button
              onClick={() => fetchData()}
              className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm"
            >
              ลองใหม่
            </button>
          </div>

          {/* Show Firebase test component when there's an error */}
          {/* <FirebaseTest />
          <FirebaseConnectionTest /> 
        </div>
      )}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex gap-2">
          <button
            onClick={() => setIsAdding(true)}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            Add Product
          </button>
          <button
            onClick={() => {
              console.log('Manual refresh triggered');
              console.log('Current state:', { isAuthenticated, authLoading, itemsLength: items.length });
              fetchData();
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
            title="Refresh data"
          >
            🔄 Refresh
          </button>
          <button
            onClick={() => {
              console.log('=== CRUD DEBUG INFO ===');
              console.log('isAuthenticated:', isAuthenticated);
              console.log('authLoading:', authLoading);
              console.log('loading:', loading);
              console.log('error:', error);
              console.log('items.length:', items.length);
              console.log('isActive:', isActive);
              console.log('======================');
            }}
            className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
            title="Debug info"
          >
            🐛 Debug
          </button>
        </div>
        <input
          type="text"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="p-2 px-4 border border-gray-300 rounded-full w-2/3 outline-none focus:ring focus:ring-blue-200"
        />
        <select
          value={rowsPerPage}
          onChange={handleRowsPerPageChange}
          className="p-2 border border-gray-300 rounded-md outline-none"
        >
          <option value={10}>10</option>
          <option value={20}>20</option>
          <option value={50}>50</option>
        </select>
      </div>

      {isAdding && (
        <AddProduct
          onClose={() => {
            setIsAdding(false);
          }}
          onProductAdded={(newItem) => {
            console.log("Product added, updating CRUD view with:", newItem);
            // Set the newly added item
            setNewlyAddedItem(newItem);
            // Reset to first page to show the newly added item
            setCurrentPage(1);
            // Scroll to top to ensure the new item is visible
            window.scrollTo(0, 0);
            // Reload the data to ensure we have the latest items
            fetchData();
          }}
        />
      )}
      {paginatedItems.length === 0 ? (
        <div className="text-center py-8">
          <svg className="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
          <p className="text-gray-500 text-lg mb-2">
            {searchQuery ? 'ไม่พบสินค้าที่ค้นหา' : 'ยังไม่มีสินค้าในระบบ'}
          </p>
          <p className="text-gray-400 text-sm">
            {searchQuery ? 'ลองเปลี่ยนคำค้นหาหรือเพิ่มสินค้าใหม่' : 'เริ่มต้นด้วยการเพิ่มสินค้าแรกของคุณ'}
          </p>
        </div>
      ) : (
        <>
          <table className="table-auto w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-center">
                <th className="border border-gray-300 px-4 py-2">No.</th>
                <th className="border border-gray-300 px-4 py-2">
                  Product Name
                </th>
                <th className="border border-gray-300 px-4 py-2">Price</th>
                <th className="border border-gray-300 px-4 py-2">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedItems.map((item, index) => {
                const typedItem = item as unknown as ProductData;
                return (
                  <tr
                    key={typedItem.id}
                    className={`hover:bg-gray-50 ${newlyAddedItem && typedItem.id === newlyAddedItem.id ? 'bg-green-100 animate-pulse' : ''}`}
                  >
                    <td className="border border-gray-300 px-4 py-2 text-center align-top">
                      {(currentPage - 1) * rowsPerPage + index + 1}
                      {newlyAddedItem && typedItem.id === newlyAddedItem.id && (
                        <span className="ml-2 text-xs bg-green-500 text-white px-1 py-0.5 rounded font-semibold">NEW</span>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 align-top">
                      {typedItem.productName}
                      {typedItem.shopeeLink && (
                        <a
                          href={typedItem.shopeeLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-orange-500 hover:text-orange-600"
                          title="View on Shopee"
                        >
                          Shopee
                        </a>
                      )}
                      {typedItem.lazadaLink && (
                        <a
                          href={typedItem.lazadaLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-500 hover:text-blue-600"
                          title="View on Lazada"
                        >
                          Lazada
                        </a>
                      )}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden ">
                      {typedItem.productType || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden ">
                      {typedItem.productDetails || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden">
                      {typedItem.filterPrice || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top hidden ">
                      {typedItem.timestamp || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-left align-top ">
                      {typedItem.price || "N/A"}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-center space-x-4 align-top">
                      <button
                        onClick={() => handleEditClick(typedItem)}
                        className="text-blue-500 hover:text-blue-600 cursor-pointer"
                        title="Edit"
                      >
                        <FiEdit size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteItem(typedItem.id!)}
                        className="text-red-500 hover:text-red-600 cursor-pointer"
                        title="Delete"
                      >
                        <FaTrashAlt size={18} />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="flex justify-center items-center my-5 space-x-4 ">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:text-black "
            >
              <GrCaretPrevious size={18} />
            </button>
            <span>
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:text-black "
            >
              <GrCaretNext size={18} />
            </button>
          </div>
        </>
      )}

      {editingItem && (
        <EditProduct
          editingItem={editingItem}
          setEditingItem={setEditingItem}
          onSave={handleSaveEdit}
          onCancel={() => setEditingItem(null)}
        />
      )}
    </div>
  );
};

export default Crud;